// Fill out your copyright notice in the Description page of Project Settings.


#include "GridMeshInstanceComponent.h"
#include "Components/InstancedStaticMeshComponent.h"

// Sets default values
UGridMeshInstanceComponent::UGridMeshInstanceComponent()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryComponentTick.bCanEverTick = false;

	InstancedStaticMesh = CreateDefaultSubobject<UInstancedStaticMeshComponent>(TEXT("InstancedStaticMesh"));
}

void UGridMeshInstanceComponent::BeginPlay()
{
	Super::BeginPlay();

}

void UGridMeshInstanceComponent::AddInstance(FTransform InstanceTransform, int Index)
{
	RemoveInstance(Index);
	InstancedStaticMesh->AddInstance(InstanceTransform);

	InstanceIndexArray.Add(Index);
}

void UGridMeshInstanceComponent::RemoveInstance(int Index)
{
	if (InstanceIndexArray.Contains(Index))
	{
		InstancedStaticMesh->RemoveInstance(Index);
		InstanceIndexArray.Remove(Index);
	}
}

void UGridMeshInstanceComponent::ClearInstances()
{
	InstancedStaticMesh->ClearInstances();
	InstanceIndexArray.Empty();
}

void UGridMeshInstanceComponent::InitializeGridMeshInstance(UStaticMesh* ShapeMesh, UMaterialInstance* ShapeMaterial, FLinearColor ShapeColor, ECollisionEnabled::Type Collision)
{
	InstancedStaticMesh->SetStaticMesh(ShapeMesh);
	InstancedStaticMesh->SetMaterial(0, ShapeMaterial);

	FVector ColorVector = FVector(ShapeColor.R, ShapeColor.G, ShapeColor.B);
	InstancedStaticMesh->SetVectorParameterValueOnMaterials("Color", ColorVector);
	InstancedStaticMesh->SetCollisionEnabled(Collision);
}

void UGridMeshInstanceComponent::SetInstanceMeshCollision(ECollisionEnabled::Type NewCollisionEnabled, FCollisionResponseContainer NewResponses)
{
	if (InstancedStaticMesh)
	{
		InstancedStaticMesh->SetCollisionEnabled(NewCollisionEnabled);
		InstancedStaticMesh->SetCollisionResponseToChannels(NewResponses);
	}
}