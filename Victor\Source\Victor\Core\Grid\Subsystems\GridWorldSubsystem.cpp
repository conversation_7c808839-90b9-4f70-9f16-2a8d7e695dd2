// Fill out your copyright notice in the Description page of Project Settings.


#include "GridWorldSubsystem.h"

void UGridWorldSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UGridWorldSubsystem::Deinitialize()
{
	Super::Deinitialize();
}

bool UGridWorldSubsystem::IsTileWalkable(ETileTypeEnum TileType)
{
	if (TileType == ETileTypeEnum::None || TileType == ETileTypeEnum::Obstacle)
	{
		return false;
	}
	else
	{
		return true;
	}
}