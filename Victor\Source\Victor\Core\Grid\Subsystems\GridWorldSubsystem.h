// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Victor/Core/Grid/Utilities/TileTypeEnum.h"
#include "GridWorldSubsystem.generated.h"

/**
 * 
 */
UCLASS()
class VICTOR_API UGridWorldSubsystem : public UWorldSubsystem
{
	GENERATED_BODY()
	
public:
	// Begin USubsystem
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;
	// End USubsystem

	bool IsTileWalkable(ETileTypeEnum TileType);
};
