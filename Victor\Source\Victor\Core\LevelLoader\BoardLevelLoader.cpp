// Fill out your copyright notice in the Description page of Project Settings.


#include "BoardLevelLoader.h"
#include "Kismet/GameplayStatics.h" // For UGameplayStatics
#include "Engine/World.h" // For UWorld

// Sets default values
ABoardLevelLoader::ABoardLevelLoader()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void ABoardLevelLoader::BeginPlay()
{
	Super::BeginPlay();

	if( BoardLevels.Num() > 0 )
	{
		CurrentLevelIndex = 0;
		PreviousLevelIndex = CurrentLevelIndex;
		LoadCurrentLevel();
	}
}

// Called every frame
void ABoardLevelLoader::Tick( float DeltaTime )
{
	Super::Tick( DeltaTime );

}

TArray<FString> ABoardLevelLoader::GetGameLevelNamesForDropdown() const
{
	TArray<FString> LevelNames;
	LevelNames.Reserve( BoardLevels.Num() ); // Pre-allocate memory for efficiency

	for( const TSoftObjectPtr<UWorld>& LevelPtr : BoardLevels )
	{
		if( !LevelPtr.IsNull() ) // Check if the reference is valid (not empty)
		{
			LevelNames.Add( LevelPtr.GetAssetName() );
		}
		else
		{
			LevelNames.Add( TEXT( "Invalid Level Reference" ) ); // Or an empty string
		}
	}
	return LevelNames;
}

void ABoardLevelLoader::UnloadPrevLevel()
{
	if( BoardLevels.IsValidIndex( PreviousLevelIndex ) )
	{
		TSoftObjectPtr<UWorld> PrevLevel = BoardLevels[ PreviousLevelIndex ];
		if( !PrevLevel.IsNull() )
		{
			FLatentActionInfo LatentInfo;
			LatentInfo.CallbackTarget = this;
			LatentInfo.ExecutionFunction = TEXT( "OnLevelUnload" ); // Function to call when loading is done
			LatentInfo.UUID = GetUniqueID();
			LatentInfo.Linkage = 0;

			UGameplayStatics::UnloadStreamLevelBySoftObjectPtr( GetWorld(), PrevLevel, LatentInfo, false );
		}
		else
		{
			UE_LOG( LogTemp, Error, TEXT( "Attempted to unload an invalid level reference at index: %d" ), PreviousLevelIndex );
		}
	}
	else
	{
		UE_LOG( LogTemp, Error, TEXT( "Invalid prev level index provided: %d" ), PreviousLevelIndex );
	}
}

void ABoardLevelLoader::LoadCurrentLevel()
{
	if( BoardLevels.IsValidIndex( CurrentLevelIndex ) )
	{
		TSoftObjectPtr<UWorld> LevelToLoad = BoardLevels[ CurrentLevelIndex ];
		if( !LevelToLoad.IsNull() )
		{
			FLatentActionInfo LatentInfo;
			LatentInfo.CallbackTarget = this;
			LatentInfo.ExecutionFunction = TEXT( "OnLevelStreamed" ); // Function to call when loading is done
			LatentInfo.UUID = GetUniqueID();
			LatentInfo.Linkage = 0;


			UGameplayStatics::LoadStreamLevelBySoftObjectPtr(
				GetWorld(),
				LevelToLoad,
				true, // bMakeVisibleAfterLoad
				false, // bShouldBlockOnLoad (set to false for true async)
				LatentInfo
			);
		}
		else
		{
			UE_LOG( LogTemp, Error, TEXT( "Attempted to load an invalid level reference at index: %d" ), CurrentLevelIndex );
		}
	}
	else
	{
		UE_LOG( LogTemp, Error, TEXT( "Invalid level index provided: %d" ), CurrentLevelIndex );
	}
}

void ABoardLevelLoader::LoadLevelByIndex( int32 LevelIndex )
{
	CurrentLevelIndex = LevelIndex;
	UnloadPrevLevel();
}

void ABoardLevelLoader::OnLevelStreamed()
{
	UE_LOG( LogTemp, Log, TEXT( "Level has been streamed" ) );
}

void ABoardLevelLoader::OnLevelUnload()
{
	PreviousLevelIndex = CurrentLevelIndex;
	LoadCurrentLevel();
}