// Fill out your copyright notice in the Description page of Project Settings.


#include "GridModifier.h"
#include "Victor/Core/Grid/GridShapes/GridShapeDataStruct.h"

// Sets default values
AGridModifier::AGridModifier()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	GridRootScene = CreateDefaultSubobject<USceneComponent>( TEXT( "GridRootScene" ) );
	SetRootComponent( GridRootScene );

	ModifierMesh = CreateDefaultSubobject<UStaticMeshComponent>( TEXT( "ModifierMesh" ) );
	ModifierMesh->SetupAttachment( GridRootScene );
}

void AGridModifier::OnConstruction( const FTransform& Transform )
{
	Super::OnConstruction( Transform );

	if( !GridShapeDataTableSoftRef.IsNull() )
	{
		GridShapeDataTableSoftRef.LoadSynchronous(); // Or use an asynchronous load function for a more performant game.
		UDataTable* GridShapeDataTable = GridShapeDataTableSoftRef.Get();
		if( GridShapeDataTable )
		{
			UEnum* EnumPtr = StaticEnum<EGridShapeEnum>();
			FString ShapeName = EnumPtr->GetNameStringByValue( ( int64 )Shape );

			FName RowName = FName( *ShapeName );

			// Use the stored data table pointer
			const FGridShapeDataStruct* GridShapeData = GridShapeDataTable->FindRow<FGridShapeDataStruct>( RowName, TEXT( "SpawnGrid" ) );
			if( GridShapeData )
			{
				TSoftObjectPtr<UStaticMesh> SoftShapeMesh = GridShapeData->Mesh;
				UStaticMesh* ShapeMesh = SoftShapeMesh.LoadSynchronous();

				ModifierMesh->SetStaticMesh( ShapeMesh );
				ModifierMesh->SetMaterial( 0, MeshMaterial );

				FVector ColorVector;
				switch( TileType )
				{
				case ETileTypeEnum::None:
					ColorVector = FVector( NoneColor.R, NoneColor.G, NoneColor.B );
					break;
				case ETileTypeEnum::Normal:
					ColorVector = FVector( NormalColor.R, NormalColor.G, NormalColor.B );
					break;
				case ETileTypeEnum::Obstacle:
					ColorVector = FVector( ObstacleColor.R, ObstacleColor.G, ObstacleColor.B );
					break;
				}

				ModifierMesh->SetVectorParameterValueOnMaterials( "Color", ColorVector );

				ModifierMesh->SetCollisionResponseToChannel( ECollisionChannel::ECC_GameTraceChannel1, ECollisionResponse::ECR_Overlap );
				SetActorHiddenInGame( true );
			}
			else
			{
				UE_LOG( LogTemp, Warning, TEXT( "shape data null" ) );
			}
		}
		else
		{
			UE_LOG( LogTemp, Warning, TEXT( "GridShapeDataTable null" ) );
		}
	}
	else
	{
		UE_LOG( LogTemp, Warning, TEXT( "GridShapeDataTableSoftRef null" ) );
	}
}

// Called when the game starts or when spawned
void AGridModifier::BeginPlay()
{
	Super::BeginPlay();

}

// Called every frame
void AGridModifier::Tick( float DeltaTime )
{
	Super::Tick( DeltaTime );

}

ETileTypeEnum AGridModifier::GetTileType()
{
	return TileType;
}