// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "BoardLevelLoader.generated.h"

UCLASS()
class VICTOR_API ABoardLevelLoader : public AActor
{
	GENERATED_BODY()

	//Fields
private:
	int CurrentLevelIndex = -1;

	int PreviousLevelIndex = -1;

protected:
	UPROPERTY( EditAnywhere, BlueprintReadOnly, Category = "Levels" )
	TArray<TSoftObjectPtr<UWorld>> BoardLevels;

	//Functions
public:
	// Sets default values for this actor's properties
	ABoardLevelLoader();

private:
	void UnloadPrevLevel();

	void LoadCurrentLevel();

	UFUNCTION()
	void OnLevelStreamed();

	UFUNCTION()
	void OnLevelUnload();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick( float DeltaTime ) override;

	TArray<FString> GetGameLevelNamesForDropdown() const;

	void LoadLevelByIndex( int32 LevelIndex );
};
