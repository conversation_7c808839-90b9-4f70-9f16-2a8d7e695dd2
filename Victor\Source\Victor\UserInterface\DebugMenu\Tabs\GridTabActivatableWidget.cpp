// Fill out your copyright notice in the Description page of Project Settings.


#include "GridTabActivatableWidget.h"
#include "Kismet/GameplayStatics.h"
#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/LevelLoader/BoardLevelLoader.h"
#include "Components/ComboBoxString.h"
#include "UObject/EnumProperty.h"
#include "Victor/Core/Grid/GridShapes/GridShapeEnum.h"
#include "Victor/UserInterface/DebugMenu/Components/Sliders/DebugSliderVector3Widget.h"
#include "Victor/UserInterface/DebugMenu/Components/Sliders/DebugSliderVector2Widget.h"
#include "Components/CheckBox.h"
#include "CommonTextBlock.h"

void UGridTabActivatableWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if( BoardLevelComboBox )
	{
		BoardLevelComboBox->OnSelectionChanged.AddDynamic( this, &UGridTabActivatableWidget::OnBoardLevelComboBoxChange );
	}

	if( ShapeComboBox )
	{
		ShapeComboBox->OnSelectionChanged.AddDynamic( this, &UGridTabActivatableWidget::OnShapeComboBoxChange );

		UEnum* EnumPtr = StaticEnum<EGridShapeEnum>();
		FString NoneName = EnumPtr->GetNameStringByValue( ( int64 )EGridShapeEnum::None );
		FString SquareName = EnumPtr->GetNameStringByValue( ( int64 )EGridShapeEnum::Square );
		FString TriangleName = EnumPtr->GetNameStringByValue( ( int64 )EGridShapeEnum::Triangle );
		FString HexagonName = EnumPtr->GetNameStringByValue( ( int64 )EGridShapeEnum::Hexagon );

		ShapeComboBox->AddOption( NoneName );
		ShapeComboBox->AddOption( SquareName );
		ShapeComboBox->AddOption( TriangleName );
		ShapeComboBox->AddOption( HexagonName );
	}

	if( LocationSpinBox )
	{
		LocationSpinBox->ValueChangeDelegate.BindUObject( this, &UGridTabActivatableWidget::OnLocationSpinBoxChange );
	}

	if( TileCountSpinBox )
	{
		TileCountSpinBox->ValueChangeDelegate.BindUObject( this, &UGridTabActivatableWidget::OnTileCountSpinBoxChange );
	}

	if( TileSizeSpinBox )
	{
		TileSizeSpinBox->ValueChangeDelegate.BindUObject( this, &UGridTabActivatableWidget::OnTileSizeSpinBoxChange );
	}

	if( UseEnvironmentCheckBox )
	{
		UseEnvironmentCheckBox->OnCheckStateChanged.AddDynamic( this, &UGridTabActivatableWidget::OnUseEnvironmentValueChange );
	}
}

void UGridTabActivatableWidget::NativeConstruct()
{
	Super::NativeConstruct();

	CurrentGrid = Cast<AGrid>( UGameplayStatics::GetActorOfClass( GetWorld(), AGrid::StaticClass() ) );
	BoardLevelLoader = Cast<ABoardLevelLoader>( UGameplayStatics::GetActorOfClass( GetWorld(), ABoardLevelLoader::StaticClass() ) );

	if( BoardLevelComboBox && BoardLevelLoader )
	{
		TArray<FString> LevelOptions = BoardLevelLoader->GetGameLevelNamesForDropdown();
		for( FString Level : LevelOptions )
		{
			BoardLevelComboBox->AddOption( Level );
		}
		BoardLevelComboBox->SetSelectedOption( LevelOptions[ 0 ] );
	}

	EGridShapeEnum Shape = CurrentGrid->GetShape();

	if( Shape == EGridShapeEnum::None )
	{
		Shape = EGridShapeEnum::Square;
	}

	UEnum* EnumPtr = StaticEnum<EGridShapeEnum>();
	FString ShapeName = EnumPtr->GetNameStringByValue( ( int64 )Shape );
	ShapeComboBox->SetSelectedOption( ShapeName );

	FVector Location = CurrentGrid->GetCenterLocation();
	FVector2D TileCount = CurrentGrid->GetTileCount();
	FVector TileSize = CurrentGrid->GetTileSize();

	LocationSpinBox->SetValue( Location );
	TileCountSpinBox->SetValue( TileCount );
	TileSizeSpinBox->SetValue( CurrentGrid->GetTileSize() );

	CurrentGrid->SpawnGrid( true );
	GetWorld()->GetTimerManager().SetTimer( DebugTimerHandle, this, &UGridTabActivatableWidget::DrawDebugLines, DrawUpdateTime, true );
}

void UGridTabActivatableWidget::OnBoardLevelComboBoxChange( FString NewValue, ESelectInfo::Type SelectionType )
{
	if( BoardLevelLoader )
	{
		int32 SelectedIndex = BoardLevelComboBox->GetSelectedIndex();
		BoardLevelLoader->LoadLevelByIndex( SelectedIndex );
	}
}

void UGridTabActivatableWidget::OnShapeComboBoxChange( FString NewValue, ESelectInfo::Type SelectionType )
{
	if( CurrentGrid )
	{
		EGridShapeEnum NewShape;
		const UEnum* EnumPtr = FindObject<UEnum>( ANY_PACKAGE, TEXT( "EGridShapeEnum" ), true );
		if( !EnumPtr )
		{
			NewShape = EGridShapeEnum::None;
		}

		int64 Value = EnumPtr->GetValueByNameString( NewValue );

		// Check if a valid enum value was found
		if( Value != INDEX_NONE )
		{
			NewShape = static_cast< EGridShapeEnum >( Value );
		}
		else
		{
			// Handle case where the string does not match any enum value
			UE_LOG( LogTemp, Warning, TEXT( "Failed to convert string '%s' to EGridShapeEnum. Setting to None." ), *NewValue );
			NewShape = EGridShapeEnum::None;
		}

		CurrentGrid->SetShape( NewShape );
		HandleGrid();
	}
}

void UGridTabActivatableWidget::OnLocationSpinBoxChange( FVector NewValue )
{
	if( CurrentGrid )
	{
		CurrentGrid->SetCenterLocation( NewValue );
		HandleGrid();
	}
}

void UGridTabActivatableWidget::OnTileCountSpinBoxChange( FVector2D NewValue )
{
	if( CurrentGrid )
	{
		FIntPoint TileCount;
		TileCount.X = static_cast< int32 >( NewValue.X );
		TileCount.Y = static_cast< int32 >( NewValue.Y );

		CurrentGrid->SetTileCount( TileCount );
		HandleGrid();
	}
}

void UGridTabActivatableWidget::OnTileSizeSpinBoxChange( FVector NewValue )
{
	if( CurrentGrid )
	{
		CurrentGrid->SetTileSize( NewValue );
		HandleGrid();
	}
}

void UGridTabActivatableWidget::OnUseEnvironmentValueChange( bool bIsChecked )
{
	if( UseEnvironmentCheckBox )
	{
		HandleGrid();
	}
}

void UGridTabActivatableWidget::HandleGrid()
{
	if( CurrentGrid && !CurrentGrid->IsSpawningGrid() )
	{
		CurrentGrid->SetIsSpawningGridForDelay();
		GetWorld()->GetTimerManager().SetTimer( DelayTimerHandle, this, &UGridTabActivatableWidget::DelayComplete, 0.05f, false );
	}
}

void UGridTabActivatableWidget::DelayComplete()
{
	bool bIsUseEnvironment = UseEnvironmentCheckBox->IsChecked();
	CurrentGrid->SpawnGrid( bIsUseEnvironment );
}

void UGridTabActivatableWidget::DrawDebugLines()
{
	if( CurrentGrid )
	{
		FString CenterLocationString = CurrentGrid->GetCenterLocation().ToString();
		FText CenterLocationText = FText::FromString( CenterLocationString );
		CenterTextBlock->SetText( CenterLocationText );

		FString BottomLeftString = CurrentGrid->GetBottomLeft().ToString();
		FText BottomLeftText = FText::FromString( BottomLeftString );
		BottomLeftTextBlock->SetText( BottomLeftText );

		if( BoundsCheckBox->IsChecked() )
		{
			FVector ExtentVector = CurrentGrid->GetCenterLocation() - CurrentGrid->GetBottomLeft();
			DrawDebugBox( GetWorld(), CurrentGrid->GetCenterLocation(), ExtentVector, FColor::Yellow, false, DrawUpdateTime );
		}

		if( CenterCheckBox->IsChecked() )
		{
			float radius = 100;
			int segments = 3;
			DrawDebugSphere( GetWorld(), CurrentGrid->GetCenterLocation(), radius, segments, FColor::Orange, false, DrawUpdateTime );
			DrawDebugSphere( GetWorld(), LocationSpinBox->GetValue(), radius, segments, FColor::Yellow, false, DrawUpdateTime );
		}

		if( BottomLeftCheckBox->IsChecked() )
		{
			float radius = 100;
			int segments = 3;
			DrawDebugSphere( GetWorld(), CurrentGrid->GetBottomLeft(), radius, segments, FColor::Red, false, DrawUpdateTime );
		}
	}

}