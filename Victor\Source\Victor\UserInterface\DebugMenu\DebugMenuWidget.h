// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "DebugMenuWidget.generated.h"

class UCommonButtonBase;
class UCommonActivatableWidgetSwitcher;

/**
 *
 */
UCLASS()
class VICTOR_API UDebugMenuWidget : public UCommonActivatableWidget
{
	GENERATED_BODY()

	//Fields
private:
	TArray<UCommonButtonBase*> TabButtonArray;

protected:
	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UCommonButtonBase* CameraTabButton;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UCommonButtonBase* CommandsTabButton;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UCommonButtonBase* GridTabButton;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UCommonActivatableWidgetSwitcher* DebugSwitcher;

	//Functions
private:
	void OnCameraBtnClick();

	void OnCommandsTabButtonClick();

	void OnGridTabButtonClick();

	void OnTabButtonClick( int32 WidgetIndex, UCommonButtonBase* Button );

public:
	void NativeOnInitialized() override;
};
